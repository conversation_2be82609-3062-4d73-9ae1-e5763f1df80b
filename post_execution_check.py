#!/usr/bin/env python3
"""
Post-execution verification script to validate that Credit entries were created correctly.

This script should be run AFTER the create_missing_credit_entries.py script to verify:
1. All transferred self payments now have Credit entries
2. Amounts match between payments and Credit entries
3. Mappings are correctly created
4. No data inconsistencies
"""

import os
import sys
from typing import List, <PERSON><PERSON>

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import create_engine, and_, func
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

from src.app.database.models import (
    Payment, 
    Khatabook, 
    KhatabookPaymentMap, 
    User,
    Person
)
from src.app.schemas.constants import KHATABOOK_ENTRY_TYPE_CREDIT

# Load environment variables
load_dotenv()

def get_database_connection():
    """Create database connection using environment variables."""
    db_username = os.getenv("DB_USERNAME")
    db_password = os.getenv("DB_PASSWORD")
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    
    database_url = f"postgresql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    return SessionLocal()

def check_data_integrity(db: Session) -> List[str]:
    """Check for data integrity issues."""
    issues = []
    
    # Check for payments without mappings
    payments_without_mappings = (
        db.query(Payment)
        .outerjoin(KhatabookPaymentMap, Payment.uuid == KhatabookPaymentMap.payment_id)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            KhatabookPaymentMap.payment_id.is_(None)
        )
        .count()
    )
    
    if payments_without_mappings > 0:
        issues.append(f"Found {payments_without_mappings} transferred self payments without khatabook mappings")
    
    # Check for mappings without Credit entries
    mappings_without_credit = (
        db.query(KhatabookPaymentMap)
        .join(Payment, KhatabookPaymentMap.payment_id == Payment.uuid)
        .outerjoin(Khatabook, and_(
            KhatabookPaymentMap.khatabook_id == Khatabook.uuid,
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT
        ))
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Khatabook.uuid.is_(None)
        )
        .count()
    )
    
    if mappings_without_credit > 0:
        issues.append(f"Found {mappings_without_credit} mappings that don't link to Credit entries")
    
    # Check for amount mismatches
    mismatched_amounts = (
        db.query(Payment, Khatabook)
        .join(KhatabookPaymentMap, Payment.uuid == KhatabookPaymentMap.payment_id)
        .join(Khatabook, KhatabookPaymentMap.khatabook_id == Khatabook.uuid)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
            Payment.amount != Khatabook.amount
        )
        .count()
    )
    
    if mismatched_amounts > 0:
        issues.append(f"Found {mismatched_amounts} payment-khatabook pairs with mismatched amounts")
    
    return issues

def get_completion_stats(db: Session) -> dict:
    """Get completion statistics."""
    # Total transferred self payments
    total_payments = (
        db.query(Payment)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False
        )
        .count()
    )
    
    # Payments with Credit entries
    payments_with_credit = (
        db.query(Payment)
        .join(KhatabookPaymentMap, Payment.uuid == KhatabookPaymentMap.payment_id)
        .join(Khatabook, KhatabookPaymentMap.khatabook_id == Khatabook.uuid)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
            Khatabook.is_deleted == False
        )
        .count()
    )
    
    # Total amounts
    total_amount = (
        db.query(func.sum(Payment.amount))
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False
        )
        .scalar() or 0
    )
    
    credit_amount = (
        db.query(func.sum(Payment.amount))
        .join(KhatabookPaymentMap, Payment.uuid == KhatabookPaymentMap.payment_id)
        .join(Khatabook, KhatabookPaymentMap.khatabook_id == Khatabook.uuid)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
            Khatabook.is_deleted == False
        )
        .scalar() or 0
    )
    
    return {
        "total_payments": total_payments,
        "payments_with_credit": payments_with_credit,
        "total_amount": total_amount,
        "credit_amount": credit_amount,
        "completion_percentage": (payments_with_credit / total_payments * 100) if total_payments > 0 else 0
    }

def main():
    """Main function to execute the post-execution check."""
    print("🔍 POST-EXECUTION VERIFICATION")
    print("=" * 60)
    print("Checking data integrity after Credit entries creation...")
    
    # Get database connection
    try:
        db = get_database_connection()
        print("✅ Database connection established")
        print(f"📊 Database: {os.getenv('DB_NAME')} on {os.getenv('DB_HOST')}")
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return
    
    try:
        # Check completion stats
        stats = get_completion_stats(db)
        
        print(f"\n📊 COMPLETION STATISTICS:")
        print(f"   📝 Total transferred self payments: {stats['total_payments']:,}")
        print(f"   ✅ Payments with Credit entries: {stats['payments_with_credit']:,}")
        print(f"   📊 Completion percentage: {stats['completion_percentage']:.1f}%")
        print(f"   💰 Total amount: ₹{stats['total_amount']:,.2f}")
        print(f"   💰 Credit entries amount: ₹{stats['credit_amount']:,.2f}")
        
        # Check data integrity
        print(f"\n🔍 DATA INTEGRITY CHECK:")
        issues = check_data_integrity(db)
        
        if not issues:
            print("   ✅ No data integrity issues found!")
            print("   ✅ All transferred self payments have Credit entries")
            print("   ✅ All mappings are correctly linked")
            print("   ✅ All amounts match between payments and Credit entries")
        else:
            print("   ❌ Data integrity issues found:")
            for issue in issues:
                print(f"      • {issue}")
        
        # Final verdict
        print(f"\n🎯 FINAL VERDICT:")
        if stats['completion_percentage'] == 100.0 and not issues:
            print("   🎉 SUCCESS! All transferred self payments now have Credit khatabook entries!")
            print("   ✅ Data integrity is maintained")
            print("   ✅ Script execution was successful")
        elif stats['completion_percentage'] == 100.0 and issues:
            print("   ⚠️  PARTIAL SUCCESS: All payments have Credit entries but there are integrity issues")
            print("   🔧 Please review and fix the issues listed above")
        else:
            print(f"   ❌ INCOMPLETE: Only {stats['completion_percentage']:.1f}% of payments have Credit entries")
            print("   🔧 You may need to run the script again or investigate issues")
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
    finally:
        db.close()
        print("\n🔒 Database connection closed.")

if __name__ == "__main__":
    main()
