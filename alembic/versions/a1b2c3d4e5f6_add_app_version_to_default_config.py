"""Add app_version field to default_config table

Revision ID: a1b2c3d4e5f6
Revises: af0c43e85b25
Create Date: 2025-08-01 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'a1b2c3d4e5f6'
down_revision = 'af0c43e85b25'
branch_labels = None
depends_on = None


def upgrade():
    op.add_column(
        'default_config',
        sa.Column('app_version', sa.String(50), nullable=False, server_default='1.0.0')
    )
    op.alter_column('default_config', 'app_version', server_default=None)


def downgrade():
    op.drop_column('default_config', 'app_version')
