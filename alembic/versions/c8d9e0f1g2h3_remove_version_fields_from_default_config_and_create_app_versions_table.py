"""remove_version_fields_from_default_config_and_create_app_versions_table

Revision ID: c8d9e0f1g2h3
Revises: b2c3d4e5f6g7
Create Date: 2025-08-02 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa
from sqlalchemy.dialects import postgresql

# revision identifiers, used by Alembic.
revision = 'c8d9e0f1g2h3'
down_revision = 'b2c3d4e5f6g7'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Create app_versions table
    op.create_table('app_versions',
    sa.Column('id', sa.Integer(), autoincrement=True, nullable=False),
    sa.Column('uuid', postgresql.UUID(as_uuid=True), nullable=False),
    sa.Column('ios_app_version', sa.String(length=50), nullable=False, server_default='1.0.0'),
    sa.Column('android_app_version', sa.String(length=50), nullable=False, server_default='1.0.0'),
    sa.Column('created_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
    sa.Column('updated_at', sa.TIMESTAMP(), server_default=sa.text('now()'), nullable=False),
    sa.Column('is_deleted', sa.Boolean(), nullable=False, server_default='false'),
    sa.PrimaryKeyConstraint('id'),
    sa.UniqueConstraint('uuid')
    )
    
    # Remove version fields from default_config table
    op.drop_column('default_config', 'android_app_version')
    op.drop_column('default_config', 'ios_app_version')
    
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    
    # Add version fields back to default_config table
    op.add_column('default_config', sa.Column('ios_app_version', sa.VARCHAR(length=50), autoincrement=False, nullable=False, server_default='1.0.0'))
    op.add_column('default_config', sa.Column('android_app_version', sa.VARCHAR(length=50), autoincrement=False, nullable=False, server_default='1.0.0'))
    
    # Drop app_versions table
    op.drop_table('app_versions')
    
    # ### end Alembic commands ###
