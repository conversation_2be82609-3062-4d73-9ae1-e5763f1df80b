"""Add ios_app_version and rename app_version to android_app_version in default_config table

Revision ID: b2c3d4e5f6g7
Revises: a1b2c3d4e5f6
Create Date: 2025-08-01 12:30:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'b2c3d4e5f6g7'
down_revision = 'a1b2c3d4e5f6'
branch_labels = None
depends_on = None


def upgrade():
    # Add ios_app_version column
    op.add_column(
        'default_config',
        sa.Column('ios_app_version', sa.String(50), nullable=False, server_default='1.0.0')
    )
    op.alter_column('default_config', 'ios_app_version', server_default=None)

    # Rename app_version to android_app_version
    op.alter_column('default_config', 'app_version', new_column_name='android_app_version')


def downgrade():
    # Rename android_app_version back to app_version
    op.alter_column('default_config', 'android_app_version', new_column_name='app_version')

    # Drop ios_app_version column
    op.drop_column('default_config', 'ios_app_version')
