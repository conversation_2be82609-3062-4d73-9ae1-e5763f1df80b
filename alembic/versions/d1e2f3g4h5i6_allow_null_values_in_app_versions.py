"""allow_null_values_in_app_versions

Revision ID: d1e2f3g4h5i6
Revises: c8d9e0f1g2h3
Create Date: 2025-01-03 12:00:00.000000

"""
from alembic import op
import sqlalchemy as sa


# revision identifiers, used by Alembic.
revision = 'd1e2f3g4h5i6'
down_revision = 'c8d9e0f1g2h3'
branch_labels = None
depends_on = None


def upgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Allow null values for ios_app_version and android_app_version columns
    op.alter_column('app_versions', 'ios_app_version',
                    existing_type=sa.VARCHAR(length=50),
                    nullable=True,
                    existing_default='1.0.0')
    
    op.alter_column('app_versions', 'android_app_version',
                    existing_type=sa.VARCHAR(length=50),
                    nullable=True,
                    existing_default='1.0.0')
    
    # Remove default values since we want to allow explicit null values
    op.alter_column('app_versions', 'ios_app_version',
                    existing_type=sa.VARCHAR(length=50),
                    nullable=True,
                    existing_default='1.0.0',
                    server_default=None)
    
    op.alter_column('app_versions', 'android_app_version',
                    existing_type=sa.VARCHAR(length=50),
                    nullable=True,
                    existing_default='1.0.0',
                    server_default=None)
    # ### end Alembic commands ###


def downgrade() -> None:
    # ### commands auto generated by Alembic - please adjust! ###
    # Restore not null constraints and default values
    # First, update any null values to default before adding not null constraint
    op.execute("UPDATE app_versions SET ios_app_version = '1.0.0' WHERE ios_app_version IS NULL")
    op.execute("UPDATE app_versions SET android_app_version = '1.0.0' WHERE android_app_version IS NULL")
    
    op.alter_column('app_versions', 'android_app_version',
                    existing_type=sa.VARCHAR(length=50),
                    nullable=False,
                    server_default='1.0.0')
    
    op.alter_column('app_versions', 'ios_app_version',
                    existing_type=sa.VARCHAR(length=50),
                    nullable=False,
                    server_default='1.0.0')
    # ### end Alembic commands ###
