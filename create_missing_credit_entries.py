#!/usr/bin/env python3
"""
<PERSON><PERSON><PERSON> to create missing Credit khatabook entries for transferred self payments.

This script identifies self payments that were transferred (non-deleted) but don't have
corresponding Credit khatabook entries, and creates those entries along with the
khatabook payment mapping.

USAGE:
    python create_missing_credit_entries.py           # Run the script
    python create_missing_credit_entries.py --dry-run # Preview what would be done

REQUIREMENTS:
    - Database connection configured in .env file
    - All required dependencies installed (sqlalchemy, python-dotenv)
    - Database should be a replica of production data

SAFETY:
    - <PERSON><PERSON><PERSON> asks for confirmation before committing changes
    - Use --dry-run to preview changes without modifying data
    - All operations are wrapped in a transaction that can be rolled back
"""

import os
import sys
from datetime import datetime
from uuid import uuid4
from typing import List, <PERSON><PERSON>

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import create_engine, and_
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

from src.app.database.models import (
    Payment, 
    <PERSON><PERSON><PERSON>ook, 
    <PERSON><PERSON><PERSON><PERSON><PERSON>ayment<PERSON><PERSON>, 
    User,
    Person
)
from src.app.schemas.constants import KHATABOOK_ENTRY_TYPE_CREDIT

# Load environment variables
load_dotenv()

def get_database_connection():
    """Create database connection using environment variables."""
    db_username = os.getenv("DB_USERNAME")
    db_password = os.getenv("DB_PASSWORD")
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    
    database_url = f"postgresql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    return SessionLocal()

def find_transferred_self_payments_without_credit_entries(db: Session) -> List[Payment]:
    """
    Find all transferred self payments that don't have corresponding Credit khatabook entries.
    
    Returns:
        List of Payment objects that need Credit khatabook entries
    """
    print("🔍 Finding transferred self payments without Credit khatabook entries...")
    
    # Get all transferred self payments (non-deleted)
    transferred_self_payments = (
        db.query(Payment)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False
        )
        .all()
    )
    
    print(f"📊 Found {len(transferred_self_payments)} transferred self payments")
    
    # Check which ones already have Credit khatabook entries
    payments_without_credit_entries = []
    
    for payment in transferred_self_payments:
        # Check if this payment already has a khatabook mapping
        existing_mapping = (
            db.query(KhatabookPaymentMap)
            .filter(KhatabookPaymentMap.payment_id == payment.uuid)
            .first()
        )
        
        if not existing_mapping:
            payments_without_credit_entries.append(payment)
        else:
            # Double check if the linked khatabook entry is Credit type
            khatabook_entry = (
                db.query(Khatabook)
                .filter(
                    Khatabook.uuid == existing_mapping.khatabook_id,
                    Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT
                )
                .first()
            )
            
            if not khatabook_entry:
                payments_without_credit_entries.append(payment)
    
    print(f"❌ Found {len(payments_without_credit_entries)} payments without Credit khatabook entries")
    
    return payments_without_credit_entries

def calculate_balance_after_entry(db: Session, user_uuid: str, payment_amount: float, payment_date: datetime) -> float:
    """
    Calculate what the user's balance should be after this payment entry.
    This is a simplified calculation - in production you might want more sophisticated logic.
    """
    # Get all transferred self payments for this user up to this date
    previous_payments = (
        db.query(Payment)
        .filter(
            Payment.created_by == user_uuid,
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Payment.transferred_date <= payment_date
        )
        .all()
    )
    
    # Calculate total credits (including this payment)
    total_credits = sum(p.amount for p in previous_payments)
    
    # Get all khatabook debit entries for this user up to this date
    debit_entries = (
        db.query(Khatabook)
        .filter(
            Khatabook.created_by == user_uuid,
            Khatabook.entry_type == "Debit",
            Khatabook.is_deleted == False,
            Khatabook.created_at <= payment_date
        )
        .all()
    )
    
    total_debits = sum(entry.amount for entry in debit_entries)
    
    return total_credits - total_debits

def create_credit_khatabook_entry(db: Session, payment: Payment) -> Tuple[bool, str]:
    """
    Create a Credit khatabook entry for a transferred self payment.
    
    Returns:
        Tuple of (success: bool, message: str)
    """
    try:
        # Calculate balance after entry
        balance_after_entry = calculate_balance_after_entry(
            db, 
            payment.created_by, 
            payment.amount, 
            payment.transferred_date or payment.created_at
        )
        
        # Create the khatabook entry
        khatabook_entry = Khatabook(
            uuid=uuid4(),
            amount=payment.amount,
            remarks=(
                f"Self payment approved - {payment.description}" 
                if payment.description 
                else "Self payment approved"
            ),
            person_id=payment.person,  # The person receiving the payment
            expense_date=payment.transferred_date or payment.created_at,
            created_by=payment.created_by,
            balance_after_entry=balance_after_entry,
            project_id=payment.project_id,
            payment_mode="Bank Transfer",  # Default payment mode for approved payments
            entry_type=KHATABOOK_ENTRY_TYPE_CREDIT,  # Self payment entries are Credit
            created_at=payment.transferred_date or payment.created_at,
            is_deleted=False,
            is_suspicious=False
        )
        
        db.add(khatabook_entry)
        db.flush()  # Get the UUID
        
        # Create the mapping entry
        mapping_entry = KhatabookPaymentMap(
            uuid=uuid4(),
            payment_id=payment.uuid,
            khatabook_id=khatabook_entry.uuid,
            created_by=payment.created_by,
            created_at=payment.transferred_date or payment.created_at
        )
        
        db.add(mapping_entry)
        
        return True, f"Created Credit entry {khatabook_entry.uuid} for payment {payment.uuid}"
        
    except Exception as e:
        return False, f"Error creating Credit entry for payment {payment.uuid}: {str(e)}"

def get_user_name(db: Session, user_uuid: str) -> str:
    """Get user name by UUID."""
    user = db.query(User).filter(User.uuid == user_uuid).first()
    return user.name if user else "Unknown User"

def get_person_name(db: Session, person_uuid: str) -> str:
    """Get person name by UUID."""
    person = db.query(Person).filter(Person.uuid == person_uuid).first()
    return person.name if person else "Unknown Person"

def print_payment_details(db: Session, payment: Payment, index: int, total: int):
    """Print detailed information about a payment."""
    user_name = get_user_name(db, payment.created_by)
    person_name = get_person_name(db, payment.person) if payment.person else "No Person"

    print(f"\n[{index}/{total}] Processing payment {payment.uuid}")
    print(f"   Amount: ₹{payment.amount:,.2f}")
    print(f"   Created by: {user_name} ({payment.created_by})")
    print(f"   Person: {person_name}")
    print(f"   Description: {payment.description or 'No description'}")
    print(f"   Transferred date: {payment.transferred_date}")
    print(f"   Created date: {payment.created_at}")

def dry_run_mode(db: Session):
    """Run the script in dry-run mode to show what would be created."""
    print("🔍 DRY RUN MODE - No changes will be made to the database")
    print("=" * 60)

    payments_needing_credit = find_transferred_self_payments_without_credit_entries(db)

    if not payments_needing_credit:
        print("🎉 No payments found that need Credit khatabook entries!")
        return

    print(f"\n📝 Would process {len(payments_needing_credit)} payments:")
    print("-" * 60)

    total_amount = 0
    for i, payment in enumerate(payments_needing_credit, 1):
        print_payment_details(db, payment, i, len(payments_needing_credit))
        total_amount += payment.amount

    print("\n" + "=" * 60)
    print(f"📊 Dry Run Summary:")
    print(f"   📝 Total payments to process: {len(payments_needing_credit)}")
    print(f"   💰 Total amount: ₹{total_amount:,.2f}")
    print(f"   📋 Would create {len(payments_needing_credit)} Credit khatabook entries")
    print(f"   🔗 Would create {len(payments_needing_credit)} khatabook-payment mappings")

def main():
    """Main function to execute the script."""
    print("🚀 Starting Credit khatabook entries creation script...")
    print("=" * 60)

    # Check if dry-run mode is requested
    dry_run = len(sys.argv) > 1 and sys.argv[1] == '--dry-run'

    # Get database connection
    try:
        db = get_database_connection()
        print("✅ Database connection established")
        print(f"📊 Database: {os.getenv('DB_NAME')} on {os.getenv('DB_HOST')}")
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return

    try:
        if dry_run:
            dry_run_mode(db)
            return

        # Find payments that need Credit entries
        payments_needing_credit = find_transferred_self_payments_without_credit_entries(db)

        if not payments_needing_credit:
            print("🎉 No payments found that need Credit khatabook entries!")
            return

        print(f"\n📝 Processing {len(payments_needing_credit)} payments...")
        print("-" * 60)

        success_count = 0
        error_count = 0

        for i, payment in enumerate(payments_needing_credit, 1):
            print_payment_details(db, payment, i, len(payments_needing_credit))

            success, message = create_credit_khatabook_entry(db, payment)

            if success:
                print(f"   ✅ {message}")
                success_count += 1
            else:
                print(f"   ❌ {message}")
                error_count += 1

        # Ask for confirmation before committing
        print("\n" + "=" * 60)
        print(f"📊 Summary:")
        print(f"   ✅ Successful: {success_count}")
        print(f"   ❌ Errors: {error_count}")
        print(f"   📝 Total processed: {len(payments_needing_credit)}")

        if success_count > 0:
            confirm = input(f"\n🤔 Do you want to commit these {success_count} changes to the database? (yes/no): ")

            if confirm.lower() in ['yes', 'y']:
                db.commit()
                print("✅ Changes committed successfully!")
                print(f"🎉 Created {success_count} Credit khatabook entries with mappings!")
            else:
                db.rollback()
                print("❌ Changes rolled back. No data was modified.")
        else:
            print("ℹ️  No changes to commit.")

    except Exception as e:
        print(f"❌ An error occurred: {e}")
        db.rollback()
    finally:
        db.close()
        print("\n🔒 Database connection closed.")

    print("\n💡 Tip: Run with --dry-run flag to see what would be processed without making changes")

def validate_environment():
    """Validate that all required environment variables are set."""
    required_vars = ["DB_USERNAME", "DB_PASSWORD", "DB_HOST", "DB_PORT", "DB_NAME"]
    missing_vars = []

    for var in required_vars:
        if not os.getenv(var):
            missing_vars.append(var)

    if missing_vars:
        print(f"❌ Missing required environment variables: {', '.join(missing_vars)}")
        print("Please check your .env file and ensure all database connection variables are set.")
        return False

    return True

if __name__ == "__main__":
    # Validate environment before running
    if not validate_environment():
        sys.exit(1)

    # Show usage if help is requested
    if len(sys.argv) > 1 and sys.argv[1] in ['--help', '-h']:
        print(__doc__)
        sys.exit(0)

    try:
        main()
    except KeyboardInterrupt:
        print("\n\n⚠️  Script interrupted by user. No changes were committed.")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Unexpected error: {e}")
        sys.exit(1)
