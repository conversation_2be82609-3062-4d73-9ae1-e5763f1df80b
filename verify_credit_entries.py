#!/usr/bin/env python3
"""
Verification script to check the current state of Credit khatabook entries for self payments.

This script provides statistics about:
1. Total transferred self payments
2. How many have Credit khatabook entries
3. How many are missing Credit entries
4. Summary statistics
"""

import os
import sys
from typing import Dict, Any

# Add the src directory to the Python path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

from sqlalchemy import create_engine, and_, func
from sqlalchemy.orm import sessionmaker, Session
from dotenv import load_dotenv

from src.app.database.models import (
    Payment, 
    Khatabook, 
    KhatabookPaymentMap, 
    User,
    Person
)
from src.app.schemas.constants import KHATABOOK_ENTRY_TYPE_CREDIT

# Load environment variables
load_dotenv()

def get_database_connection():
    """Create database connection using environment variables."""
    db_username = os.getenv("DB_USERNAME")
    db_password = os.getenv("DB_PASSWORD")
    db_host = os.getenv("DB_HOST")
    db_port = os.getenv("DB_PORT")
    db_name = os.getenv("DB_NAME")
    
    database_url = f"postgresql://{db_username}:{db_password}@{db_host}:{db_port}/{db_name}"
    
    engine = create_engine(database_url)
    SessionLocal = sessionmaker(autocommit=False, autoflush=False, bind=engine)
    
    return SessionLocal()

def get_statistics(db: Session) -> Dict[str, Any]:
    """Get comprehensive statistics about self payments and Credit entries."""
    
    # Total transferred self payments (non-deleted)
    total_transferred_self_payments = (
        db.query(Payment)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False
        )
        .count()
    )
    
    # Total amount of transferred self payments
    total_amount = (
        db.query(func.sum(Payment.amount))
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False
        )
        .scalar() or 0
    )
    
    # Self payments with Credit khatabook entries (via mapping)
    payments_with_credit_entries = (
        db.query(Payment)
        .join(KhatabookPaymentMap, Payment.uuid == KhatabookPaymentMap.payment_id)
        .join(Khatabook, KhatabookPaymentMap.khatabook_id == Khatabook.uuid)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
            Khatabook.is_deleted == False
        )
        .count()
    )
    
    # Amount of self payments with Credit entries
    amount_with_credit_entries = (
        db.query(func.sum(Payment.amount))
        .join(KhatabookPaymentMap, Payment.uuid == KhatabookPaymentMap.payment_id)
        .join(Khatabook, KhatabookPaymentMap.khatabook_id == Khatabook.uuid)
        .filter(
            Payment.self_payment == True,
            Payment.status == "transferred",
            Payment.is_deleted == False,
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
            Khatabook.is_deleted == False
        )
        .scalar() or 0
    )
    
    # Total Credit khatabook entries
    total_credit_entries = (
        db.query(Khatabook)
        .filter(
            Khatabook.entry_type == KHATABOOK_ENTRY_TYPE_CREDIT,
            Khatabook.is_deleted == False
        )
        .count()
    )
    
    # Total khatabook payment mappings
    total_mappings = db.query(KhatabookPaymentMap).count()
    
    return {
        "total_transferred_self_payments": total_transferred_self_payments,
        "total_amount": total_amount,
        "payments_with_credit_entries": payments_with_credit_entries,
        "amount_with_credit_entries": amount_with_credit_entries,
        "payments_missing_credit_entries": total_transferred_self_payments - payments_with_credit_entries,
        "amount_missing_credit_entries": total_amount - amount_with_credit_entries,
        "total_credit_entries": total_credit_entries,
        "total_mappings": total_mappings,
        "completion_percentage": (payments_with_credit_entries / total_transferred_self_payments * 100) if total_transferred_self_payments > 0 else 0
    }

def print_statistics(stats: Dict[str, Any]):
    """Print formatted statistics."""
    print("📊 CREDIT KHATABOOK ENTRIES VERIFICATION REPORT")
    print("=" * 60)
    
    print(f"\n🔍 TRANSFERRED SELF PAYMENTS:")
    print(f"   📝 Total count: {stats['total_transferred_self_payments']:,}")
    print(f"   💰 Total amount: ₹{stats['total_amount']:,.2f}")
    
    print(f"\n✅ WITH CREDIT ENTRIES:")
    print(f"   📝 Count: {stats['payments_with_credit_entries']:,}")
    print(f"   💰 Amount: ₹{stats['amount_with_credit_entries']:,.2f}")
    print(f"   📊 Percentage: {stats['completion_percentage']:.1f}%")
    
    print(f"\n❌ MISSING CREDIT ENTRIES:")
    print(f"   📝 Count: {stats['payments_missing_credit_entries']:,}")
    print(f"   💰 Amount: ₹{stats['amount_missing_credit_entries']:,.2f}")
    print(f"   📊 Percentage: {100 - stats['completion_percentage']:.1f}%")
    
    print(f"\n📋 OVERALL STATISTICS:")
    print(f"   🏷️  Total Credit khatabook entries: {stats['total_credit_entries']:,}")
    print(f"   🔗 Total khatabook-payment mappings: {stats['total_mappings']:,}")
    
    if stats['payments_missing_credit_entries'] > 0:
        print(f"\n⚠️  ACTION REQUIRED:")
        print(f"   Run the create_missing_credit_entries.py script to create")
        print(f"   {stats['payments_missing_credit_entries']:,} missing Credit entries")
        print(f"   totaling ₹{stats['amount_missing_credit_entries']:,.2f}")
    else:
        print(f"\n🎉 ALL GOOD!")
        print(f"   All transferred self payments have Credit khatabook entries!")

def main():
    """Main function to execute the verification."""
    print("🚀 Starting Credit khatabook entries verification...")
    print("=" * 60)
    
    # Get database connection
    try:
        db = get_database_connection()
        print("✅ Database connection established")
        print(f"📊 Database: {os.getenv('DB_NAME')} on {os.getenv('DB_HOST')}")
    except Exception as e:
        print(f"❌ Failed to connect to database: {e}")
        return
    
    try:
        # Get statistics
        stats = get_statistics(db)
        
        # Print results
        print_statistics(stats)
        
    except Exception as e:
        print(f"❌ An error occurred: {e}")
    finally:
        db.close()
        print("\n🔒 Database connection closed.")

if __name__ == "__main__":
    main()
