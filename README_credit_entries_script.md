# Credit Khatabook Entries Creation Script

## Overview

This script identifies self payments that were transferred (non-deleted) but don't have corresponding Credit khatabook entries, and creates those entries along with the khatabook payment mapping.

## Background

When the feature was introduced to automatically create Credit khatabook entries for transferred self payments, there were already existing self payments in the database that had been transferred but didn't have the corresponding Credit entries. This script addresses that gap by:

1. Finding all transferred self payments that don't have Credit khatabook entries
2. Creating the missing Credit khatabook entries
3. Creating the khatabook-payment mappings to link them

## Prerequisites

1. **Database Access**: Ensure your `.env` file is configured with the correct database connection details
2. **Python Dependencies**: Make sure you have the required packages installed:
   ```bash
   pip install sqlalchemy python-dotenv
   ```
3. **Database Backup**: Since this script modifies data, ensure you're working with a replica/backup of production data

## Usage

### Dry Run (Recommended First Step)
```bash
python create_missing_credit_entries.py --dry-run
```
This will show you exactly what payments would be processed without making any changes to the database.

### Actual Execution
```bash
python create_missing_credit_entries.py
```
This will process the payments and ask for confirmation before committing changes.

### Help
```bash
python create_missing_credit_entries.py --help
```

## What the Script Does

1. **Connects to Database**: Uses environment variables from `.env` file
2. **Finds Missing Entries**: Identifies transferred self payments without Credit khatabook entries
3. **Creates Credit Entries**: For each missing entry, creates:
   - A Credit khatabook entry with the same amount as the payment
   - Proper balance calculation
   - Appropriate timestamps and references
4. **Creates Mappings**: Links the khatabook entry to the payment via `KhatabookPaymentMap`
5. **Asks for Confirmation**: Before committing changes to the database

## Safety Features

- **Dry Run Mode**: Preview changes without modifying data
- **Transaction Safety**: All operations are wrapped in a database transaction
- **User Confirmation**: Script asks for explicit confirmation before committing
- **Rollback on Error**: Any errors will rollback all changes
- **Detailed Logging**: Shows exactly what's being processed

## Output Example

```
🚀 Starting Credit khatabook entries creation script...
============================================================
✅ Database connection established
📊 Database: ipm_prod_backup on localhost

🔍 Finding transferred self payments without Credit khatabook entries...
📊 Found 15 transferred self payments
❌ Found 8 payments without Credit khatabook entries

📝 Processing 8 payments...
------------------------------------------------------------

[1/8] Processing payment 12345678-1234-1234-1234-123456789012
   Amount: ₹10,000.00
   Created by: John Doe (87654321-4321-4321-4321-210987654321)
   Person: John Doe
   Description: Materials purchase
   Transferred date: 2024-07-15 10:30:00
   Created date: 2024-07-14 09:15:00
   ✅ Created Credit entry abcdef12-3456-7890-abcd-ef1234567890 for payment 12345678-1234-1234-1234-123456789012

...

============================================================
📊 Summary:
   ✅ Successful: 8
   ❌ Errors: 0
   📝 Total processed: 8

🤔 Do you want to commit these 8 changes to the database? (yes/no): yes
✅ Changes committed successfully!
🎉 Created 8 Credit khatabook entries with mappings!

🔒 Database connection closed.

💡 Tip: Run with --dry-run flag to see what would be processed without making changes
```

## Database Tables Affected

- `khatabook_entries`: New Credit entries will be created
- `khatabook_payment_map`: New mapping entries linking payments to khatabook entries

## Verification

After running the script, you can verify the results by checking:

1. **Credit Entries Created**: 
   ```sql
   SELECT COUNT(*) FROM khatabook_entries WHERE entry_type = 'Credit';
   ```

2. **Mappings Created**:
   ```sql
   SELECT COUNT(*) FROM khatabook_payment_map;
   ```

3. **Self Payments with Credit Entries**:
   ```sql
   SELECT p.uuid, p.amount, k.uuid as khatabook_uuid
   FROM payments p
   JOIN khatabook_payment_map kpm ON p.uuid = kpm.payment_id
   JOIN khatabook_entries k ON kpm.khatabook_id = k.uuid
   WHERE p.self_payment = true 
   AND p.status = 'transferred' 
   AND k.entry_type = 'Credit';
   ```

## Troubleshooting

1. **Database Connection Issues**: Check your `.env` file configuration
2. **Permission Errors**: Ensure the script has read/write access to the database
3. **Missing Dependencies**: Install required Python packages
4. **Data Inconsistencies**: Use dry-run mode first to identify any issues

## Important Notes

- This script is designed to be run **once** to fix historical data
- Running it multiple times on the same data should be safe (it checks for existing entries)
- Always test with `--dry-run` first
- Keep a backup of your database before running
- The script calculates balance_after_entry based on existing data patterns
